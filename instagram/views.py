import asyncio
import json
from rest_framework import views
from rest_framework.response import Response
from rest_framework import status

from instagram.serializers import DownloadSerializer
from instagram.models import DownloadTask

from django_redis import get_redis_connection


class DownloadView(views.APIView):
    serializer_class = DownloadSerializer
    authentication_classes = []
    permission_classes = []

    def post(self, request, *args, **kwargs):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)

        task = DownloadTask.objects.create(
            url=serializer.validated_data.get("url", ""),
            hosting=serializer.validated_data.get("hosting", "instagram"),
            bot_token=serializer.validated_data.get("bot_token", ""),
            bot_username=serializer.validated_data.get("bot_username", ""),
            chat_id=serializer.validated_data.get("chat_id", ""),
            status=DownloadTask.TaskStatus.PENDING
        )

        redis_client = get_redis_connection("default")

        queue_data = {
            "task_id": str(task.task_id),
            "url": serializer.validated_data.get("url", ""),
            "bot_username": serializer.validated_data.get("bot_username", ""),
            "hosting": "instagram",
            "bot_token": serializer.validated_data.get("bot_token", ""),
            "chat_id": serializer.validated_data.get("chat_id", "")
        }

        redis_client.lpush("download_queue", json.dumps(queue_data))

        return Response({
            "task_id": str(task.task_id),
            "status": task.status,
            "message": "Task created successfully"
        }, status=status.HTTP_201_CREATED)


class CheckTaskView(views.APIView):
    """
    API endpoint to check the status of a download task
    """
    authentication_classes = []
    permission_classes = []

    def get(self, request, task_id, *args, **kwargs):
        try:
            task = DownloadTask.objects.get(task_id=task_id)

            response_data = {
                "task_id": str(task.task_id),
                "status": task.status,
                "url": task.url,
                "hosting": task.hosting,
            }

            if task.message_id:
                response_data["message_id"] = task.message_id

            if task.status == DownloadTask.TaskStatus.FAILED and task.error_message:
                response_data["error_message"] = task.error_message

            return Response(response_data, status=status.HTTP_200_OK)

        except DownloadTask.DoesNotExist:
            return Response({
                "error": "Task not found",
                "task_id": task_id
            }, status=status.HTTP_404_NOT_FOUND)
